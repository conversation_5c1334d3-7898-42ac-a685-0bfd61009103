"""
性能监控和优化工具
用于监控系统性能并提供优化建议
"""

import time
import psutil
import threading
import logging
from collections import deque
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.frame_times = deque(maxlen=window_size)
        self.detection_times = deque(maxlen=window_size)
        self.streaming_times = deque(maxlen=window_size)
        self.total_times = deque(maxlen=window_size)
        
        self.start_time = time.time()
        self.frame_count = 0
        self.skip_count = 0
        
        self._lock = threading.Lock()
        
    def record_frame(self, detection_time: float, streaming_time: float):
        """记录帧处理时间"""
        with self._lock:
            current_time = time.time()
            total_time = detection_time + streaming_time
            
            self.frame_times.append(current_time)
            self.detection_times.append(detection_time)
            self.streaming_times.append(streaming_time)
            self.total_times.append(total_time)
            
            self.frame_count += 1
    
    def record_skip(self):
        """记录跳帧"""
        with self._lock:
            self.skip_count += 1
    
    def get_stats(self) -> Dict:
        """获取性能统计"""
        with self._lock:
            if not self.frame_times:
                return {}
            
            # 计算FPS
            if len(self.frame_times) > 1:
                time_span = self.frame_times[-1] - self.frame_times[0]
                fps = (len(self.frame_times) - 1) / time_span if time_span > 0 else 0
            else:
                fps = 0
            
            # 计算平均时间
            avg_detection = sum(self.detection_times) / len(self.detection_times)
            avg_streaming = sum(self.streaming_times) / len(self.streaming_times)
            avg_total = sum(self.total_times) / len(self.total_times)
            
            # 计算最大时间
            max_detection = max(self.detection_times) if self.detection_times else 0
            max_streaming = max(self.streaming_times) if self.streaming_times else 0
            max_total = max(self.total_times) if self.total_times else 0
            
            # 系统资源使用
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            return {
                'fps': fps,
                'frame_count': self.frame_count,
                'skip_count': self.skip_count,
                'skip_rate': self.skip_count / self.frame_count if self.frame_count > 0 else 0,
                'avg_detection_time': avg_detection,
                'avg_streaming_time': avg_streaming,
                'avg_total_time': avg_total,
                'max_detection_time': max_detection,
                'max_streaming_time': max_streaming,
                'max_total_time': max_total,
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'uptime': time.time() - self.start_time
            }
    
    def get_recommendations(self) -> List[str]:
        """获取性能优化建议"""
        stats = self.get_stats()
        recommendations = []
        
        if not stats:
            return recommendations
        
        # FPS相关建议
        if stats['fps'] < 20:
            recommendations.append("FPS过低，建议降低分辨率或启用更多跳帧")
        
        # 检测时间建议
        if stats['avg_detection_time'] > 0.05:  # 50ms
            recommendations.append("检测时间过长，建议使用GPU加速或降低模型复杂度")
        
        # 流媒体时间建议
        if stats['avg_streaming_time'] > 0.02:  # 20ms
            recommendations.append("流媒体编码时间过长，建议优化FFmpeg参数")
        
        # 跳帧率建议
        if stats['skip_rate'] > 0.1:  # 10%
            recommendations.append("跳帧率过高，系统负载过重")
        
        # CPU使用率建议
        if stats['cpu_percent'] > 80:
            recommendations.append("CPU使用率过高，建议优化处理算法或增加硬件资源")
        
        # 内存使用建议
        if stats['memory_percent'] > 80:
            recommendations.append("内存使用率过高，建议检查内存泄漏或增加内存")
        
        return recommendations

class LatencyOptimizer:
    """延时优化器"""
    
    def __init__(self, target_latency_ms: float = 100):
        self.target_latency_ms = target_latency_ms
        self.current_skip_frames = 0
        self.max_skip_frames = 5
        
    def should_skip_frame(self, processing_time_ms: float) -> bool:
        """判断是否应该跳帧"""
        if processing_time_ms > self.target_latency_ms:
            self.current_skip_frames = min(self.current_skip_frames + 1, self.max_skip_frames)
            return True
        else:
            self.current_skip_frames = max(self.current_skip_frames - 1, 0)
            return self.current_skip_frames > 0
    
    def get_optimal_resolution(self, current_fps: float, target_fps: float = 25) -> str:
        """根据当前性能推荐最优分辨率"""
        if current_fps >= target_fps:
            return "640x640"  # 保持当前分辨率
        elif current_fps >= target_fps * 0.8:
            return "512x512"  # 轻微降低
        elif current_fps >= target_fps * 0.6:
            return "416x416"  # 中等降低
        else:
            return "320x320"  # 大幅降低

def log_performance_stats(monitor: PerformanceMonitor, interval: int = 30):
    """定期记录性能统计"""
    def _log_stats():
        while True:
            time.sleep(interval)
            stats = monitor.get_stats()
            recommendations = monitor.get_recommendations()
            
            logger.info(f"Performance Stats: FPS={stats.get('fps', 0):.2f}, "
                       f"Avg Detection={stats.get('avg_detection_time', 0)*1000:.1f}ms, "
                       f"Avg Streaming={stats.get('avg_streaming_time', 0)*1000:.1f}ms, "
                       f"CPU={stats.get('cpu_percent', 0):.1f}%, "
                       f"Memory={stats.get('memory_percent', 0):.1f}%")
            
            if recommendations:
                logger.warning(f"Performance Recommendations: {'; '.join(recommendations)}")
    
    thread = threading.Thread(target=_log_stats, daemon=True)
    thread.start()
    return thread

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()
latency_optimizer = LatencyOptimizer()
