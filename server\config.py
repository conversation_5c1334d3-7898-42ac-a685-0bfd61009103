import os

# 模型路径
MODEL_PATH = os.path.join('models', 'best.pt')

# RTMP配置
RTMP_SERVER = 'rtmp://localhost/live'
RTMP_STREAM = 'stream'
RTMP_URL = f"{RTMP_SERVER}/{RTMP_STREAM}"

# 摄像头默认配置 - 低延时优化
DEFAULT_CAMERA_INDEX = 0
DEFAULT_RESOLUTION = '640x640'  # 保持正方形分辨率，适合YOLOv8
DEFAULT_FPS = 30

# 性能优化配置
PERFORMANCE_CONFIG = {
    # 检测优化
    'detection': {
        'confidence_threshold': 0.5,  # 置信度阈值
        'iou_threshold': 0.45,        # NMS IoU阈值
        'max_detections': 100,        # 最大检测数量
        'input_size': 640,            # 模型输入尺寸
        'use_half_precision': True,   # 使用半精度推理
        'warmup_iterations': 3,       # 模型预热次数
    },

    # 流媒体优化
    'streaming': {
        'video_bitrate': '1000k',     # 视频码率
        'gop_size': 1,                # GOP大小(关键帧间隔)
        'preset': 'ultrafast',        # FFmpeg编码预设
        'tune': 'zerolatency',        # 调优参数
        'profile': 'baseline',        # H.264 profile
        'buffer_size': '500k',        # 缓冲区大小
    },

    # 摄像头优化
    'camera': {
        'buffer_size': 1,             # 摄像头缓冲区大小
        'fourcc': 'MJPG',            # 视频格式
        'auto_exposure': False,       # 自动曝光
        'auto_focus': False,          # 自动对焦
        'auto_wb': False,             # 自动白平衡
        'exposure_value': -6,         # 曝光值
    },

    # 处理优化
    'processing': {
        'max_skip_frames': 2,         # 最大跳帧数
        'performance_log_interval': 50, # 性能日志间隔
        'fps_log_interval': 100,      # FPS日志间隔
        'enable_frame_skip': True,    # 启用智能跳帧
        'target_latency_ms': 100,     # 目标延时(毫秒)
    }
}

# 其他配置
DEBUG = True

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'performance_logging': True,  # 启用性能日志
}
