# YOLOv8 低延时检测系统 - 简单使用说明

## 🚀 一键启动

### 方法1: 双击启动 (推荐)
```
双击 start.bat 文件
```

### 方法2: 命令行启动
```bash
python simple_start.py
```

## 🧪 性能测试

### 测试系统性能
```
双击 test.bat 文件
```
或
```bash
python simple_test.py
```

## 📱 使用流程

### 1. 启动系统
- 双击 `start.bat`
- 等待看到 "🎉 系统启动成功!" 消息

### 2. 启动客户端
- 打开新的命令行窗口
- 运行:
```bash
cd client
python main.py
```

### 3. 开始检测
- 在客户端界面选择摄像头
- 点击"开始检测"按钮
- 实时查看检测结果

## ⚡ 低延时优化

系统已自动应用以下优化:

### Nginx RTMP 优化
- ✅ chunk_size: 128 (降低传输延时)
- ✅ 缓冲时间: 100ms (减少缓冲延时)
- ✅ 同步阈值: 10ms (提高同步性)

### FFmpeg 编码优化
- ✅ ultrafast 预设 + zerolatency 调优
- ✅ GOP大小为1 (每帧都是关键帧)
- ✅ 禁用缓冲 (立即传输)

### 摄像头优化
- ✅ 缓冲区大小: 1帧 (最小延时)
- ✅ MJPEG格式 (快速处理)
- ✅ 禁用自动功能 (避免延时)

### 检测优化
- ✅ GPU加速 + 半精度推理
- ✅ 模型预热 (消除首次延时)
- ✅ 智能跳帧 (自适应负载)

## 📊 预期性能

| 延时类型 | 优化前 | 优化后 | 提升 |
|---------|--------|--------|------|
| 总延时 | 2-3秒 | 100-300ms | 90%+ |
| 检测延时 | 100-200ms | 20-50ms | 75% |
| 传输延时 | 1-2秒 | 50-100ms | 95% |

## 🔧 故障排除

### 常见问题

1. **"Python 未安装"**
   - 安装 Python 3.8+ 并添加到 PATH

2. **"FFmpeg 未安装"**
   - 下载 FFmpeg 并添加到 PATH

3. **"模型文件不存在"**
   - 确保 `server/models/best.pt` 文件存在

4. **"摄像头无法打开"**
   - 检查摄像头是否被其他程序占用
   - 尝试更改摄像头索引

5. **延时仍然很高**
   - 运行 `test.bat` 查看性能分析
   - 根据建议调整设置

### 性能优化建议

1. **如果检测慢**:
   - 确保使用 NVIDIA GPU
   - 降低分辨率 (640x640 → 512x512)

2. **如果传输慢**:
   - 检查网络连接
   - 确保 Nginx 正常运行

3. **如果摄像头慢**:
   - 使用 USB 3.0 接口
   - 检查摄像头驱动

## 📁 文件说明

- `start.bat` - 一键启动脚本
- `test.bat` - 性能测试脚本
- `simple_start.py` - Python启动器
- `simple_test.py` - Python测试器
- `server/nginx-rtmp/nginx.conf` - 优化的Nginx配置
- `server/config.py` - 系统配置文件

## 🎯 使用技巧

1. **首次使用**: 先运行 `test.bat` 检查性能
2. **调试模式**: 查看命令行输出了解系统状态
3. **性能监控**: 关注FPS和延时数据
4. **网络状态**: 访问 http://localhost:8080/stat 查看RTMP状态

## 💡 优化原理

通过以下技术实现低延时:

1. **减少缓冲**: 所有环节最小化缓冲区
2. **并行处理**: 检测和编码同时进行
3. **硬件加速**: 充分利用GPU和硬件编码
4. **智能跳帧**: 根据负载动态调整
5. **网络优化**: 小数据包快速传输

---

**需要帮助?** 查看命令行输出的错误信息和建议
