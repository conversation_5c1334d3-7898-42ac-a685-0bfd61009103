@echo off
echo ========================================
echo YOLOv8 Low-Latency Detection System
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to PATH
    pause
    exit /b 1
)

REM 检查FFmpeg是否安装
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo Error: FFmpeg is not installed or not in PATH
    echo Please install FFmpeg and add it to PATH
    pause
    exit /b 1
)

REM 检查模型文件
if not exist "server\models\best.pt" (
    echo Error: YOLOv8 model file not found
    echo Please ensure server\models\best.pt exists
    pause
    exit /b 1
)

echo All dependencies check passed!
echo.

REM 设置环境变量优化
set OMP_NUM_THREADS=%NUMBER_OF_PROCESSORS%
set MKL_NUM_THREADS=%NUMBER_OF_PROCESSORS%
set CUDA_VISIBLE_DEVICES=0

echo Starting optimized low-latency system...
echo.

REM 启动优化启动器
python start_optimized.py

pause
