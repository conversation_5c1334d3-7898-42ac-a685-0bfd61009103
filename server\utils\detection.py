from ultralytics import YOLO
import cv2
import numpy as np
import torch

class Detector:
    def __init__(self, model_path):
        # 检查CUDA可用性
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"Using device: {self.device}")

        # 加载模型并优化
        self.model = YOLO(model_path)
        self.model.to(self.device)

        # 如果使用GPU，启用半精度推理
        if self.device == 'cuda':
            self.model.half()  # 使用FP16半精度推理，提高速度

        self.last_counts = {}
        self.class_names = self.model.names

        # 预热模型 - 运行一次推理来初始化
        dummy_frame = np.zeros((640, 640, 3), dtype=np.uint8)
        _ = self.model(dummy_frame, verbose=False)
        print("Model warmed up successfully")

    def detect(self, frame):
        # 运行YOLOv8检测 - 低延时优化
        results = self.model(
            frame,
            verbose=False,
            device=self.device,
            half=True if self.device == 'cuda' else False,  # 使用半精度
            imgsz=640,  # 固定输入尺寸
            conf=0.5,   # 置信度阈值
            iou=0.45,   # NMS IoU阈值
            max_det=100,  # 最大检测数量
            agnostic_nms=False,  # 类别无关NMS
            retina_masks=False,  # 禁用高分辨率掩码
            save=False,  # 不保存结果
            save_txt=False,  # 不保存文本
            save_conf=False,  # 不保存置信度
            save_crop=False,  # 不保存裁剪图像
            show=False,  # 不显示结果
            stream=True  # 流式处理
        )

        # 解析结果
        result = next(iter(results))  # 获取第一个结果
        boxes = result.boxes
        self.last_counts = {}

        # 绘制检测结果 - 优化绘制性能
        annotated_frame = result.plot(
            conf=True,  # 显示置信度
            line_width=2,  # 线宽
            font_size=12,  # 字体大小
            pil=False  # 使用OpenCV而不是PIL
        )

        # 统计各类别数量
        if boxes is not None and len(boxes) > 0:
            for class_id in boxes.cls.unique():
                class_name = self.class_names[int(class_id)]
                count = len(boxes[boxes.cls == class_id])
                self.last_counts[class_name] = count

        return annotated_frame, self.last_counts

    def get_last_counts(self):
        return self.last_counts
