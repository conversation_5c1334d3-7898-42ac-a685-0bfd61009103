#!/usr/bin/env python3
"""
优化启动脚本
用于启动低延时优化的YOLOv8检测系统
"""

import os
import sys
import subprocess
import logging
import time
import signal
import psutil
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OptimizedLauncher:
    """优化启动器"""
    
    def __init__(self):
        self.nginx_process = None
        self.flask_process = None
        self.project_root = Path(__file__).parent
        
    def check_dependencies(self):
        """检查依赖项"""
        logger.info("Checking dependencies...")
        
        # 检查Python包
        required_packages = [
            'torch', 'ultralytics', 'opencv-python', 
            'flask', 'flask-restx', 'psutil', 'numpy'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                logger.info(f"✓ {package} is installed")
            except ImportError:
                missing_packages.append(package)
                logger.error(f"✗ {package} is missing")
        
        if missing_packages:
            logger.error(f"Missing packages: {missing_packages}")
            logger.info("Install missing packages with: pip install " + " ".join(missing_packages))
            return False
        
        # 检查FFmpeg
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                logger.info("✓ FFmpeg is available")
            else:
                logger.error("✗ FFmpeg is not working properly")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.error("✗ FFmpeg is not installed or not in PATH")
            logger.info("Please install FFmpeg and add it to your PATH")
            return False
        
        # 检查模型文件
        model_path = self.project_root / "server" / "models" / "best.pt"
        if model_path.exists():
            logger.info("✓ YOLOv8 model file found")
        else:
            logger.error(f"✗ Model file not found: {model_path}")
            logger.info("Please ensure the YOLOv8 model file is in server/models/best.pt")
            return False
        
        return True
    
    def optimize_system(self):
        """优化系统设置"""
        logger.info("Applying system optimizations...")
        
        try:
            # 设置进程优先级
            current_process = psutil.Process()
            current_process.nice(psutil.HIGH_PRIORITY_CLASS if os.name == 'nt' else -10)
            logger.info("✓ Set high process priority")
        except Exception as e:
            logger.warning(f"Could not set process priority: {e}")
        
        # 设置环境变量优化
        os.environ['OMP_NUM_THREADS'] = str(psutil.cpu_count())
        os.environ['MKL_NUM_THREADS'] = str(psutil.cpu_count())
        os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # 使用第一个GPU
        logger.info("✓ Set optimization environment variables")
        
        # 检查GPU可用性
        try:
            import torch
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                logger.info(f"✓ GPU available: {gpu_name} ({gpu_memory:.1f}GB)")
            else:
                logger.warning("⚠ No GPU available, using CPU (performance may be limited)")
        except ImportError:
            logger.warning("⚠ PyTorch not available for GPU check")
    
    def start_nginx(self):
        """启动Nginx RTMP服务器"""
        logger.info("Starting Nginx RTMP server...")
        
        nginx_conf = self.project_root / "server" / "nginx-rtmp" / "nginx.conf"
        nginx_exe = self.project_root / "nginx-rtmp-win32-master" / "nginx-rtmp-win32-master" / "nginx.exe"
        
        if not nginx_exe.exists():
            logger.error(f"Nginx executable not found: {nginx_exe}")
            return False
        
        if not nginx_conf.exists():
            logger.error(f"Nginx config not found: {nginx_conf}")
            return False
        
        try:
            # 停止可能运行的nginx进程
            self.stop_nginx()
            
            # 启动nginx
            cmd = [str(nginx_exe), '-c', str(nginx_conf)]
            self.nginx_process = subprocess.Popen(
                cmd,
                cwd=str(nginx_exe.parent),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待启动
            time.sleep(2)
            
            if self.nginx_process.poll() is None:
                logger.info("✓ Nginx RTMP server started successfully")
                return True
            else:
                stdout, stderr = self.nginx_process.communicate()
                logger.error(f"Nginx failed to start: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to start Nginx: {e}")
            return False
    
    def stop_nginx(self):
        """停止Nginx进程"""
        try:
            # 查找并终止nginx进程
            for proc in psutil.process_iter(['pid', 'name']):
                if 'nginx' in proc.info['name'].lower():
                    proc.terminate()
                    logger.info(f"Terminated nginx process {proc.info['pid']}")
        except Exception as e:
            logger.warning(f"Error stopping nginx: {e}")
    
    def start_flask(self):
        """启动Flask应用"""
        logger.info("Starting Flask application...")
        
        app_path = self.project_root / "server" / "app.py"
        if not app_path.exists():
            logger.error(f"Flask app not found: {app_path}")
            return False
        
        try:
            # 设置Python路径
            env = os.environ.copy()
            env['PYTHONPATH'] = str(self.project_root / "server")
            
            # 启动Flask应用
            cmd = [sys.executable, str(app_path)]
            self.flask_process = subprocess.Popen(
                cmd,
                cwd=str(app_path.parent),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            logger.info("✓ Flask application started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Flask: {e}")
            return False
    
    def monitor_processes(self):
        """监控进程状态"""
        logger.info("Monitoring processes... Press Ctrl+C to stop")
        
        try:
            while True:
                # 检查nginx状态
                if self.nginx_process and self.nginx_process.poll() is not None:
                    logger.error("Nginx process died, restarting...")
                    self.start_nginx()
                
                # 检查flask状态
                if self.flask_process and self.flask_process.poll() is not None:
                    logger.error("Flask process died, restarting...")
                    self.start_flask()
                
                # 输出Flask日志
                if self.flask_process:
                    try:
                        line = self.flask_process.stdout.readline()
                        if line:
                            print(f"[Flask] {line.strip()}")
                    except:
                        pass
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, shutting down...")
            self.shutdown()
    
    def shutdown(self):
        """关闭所有进程"""
        logger.info("Shutting down...")
        
        if self.flask_process:
            self.flask_process.terminate()
            try:
                self.flask_process.wait(timeout=5)
                logger.info("✓ Flask process terminated")
            except subprocess.TimeoutExpired:
                self.flask_process.kill()
                logger.info("✓ Flask process killed")
        
        self.stop_nginx()
        logger.info("✓ All processes stopped")
    
    def run(self):
        """运行优化启动流程"""
        logger.info("=== YOLOv8 Low-Latency Detection System ===")
        
        # 检查依赖
        if not self.check_dependencies():
            logger.error("Dependency check failed, exiting...")
            return False
        
        # 优化系统
        self.optimize_system()
        
        # 启动服务
        if not self.start_nginx():
            logger.error("Failed to start Nginx, exiting...")
            return False
        
        if not self.start_flask():
            logger.error("Failed to start Flask, exiting...")
            self.shutdown()
            return False
        
        # 监控进程
        self.monitor_processes()
        
        return True

def main():
    """主函数"""
    launcher = OptimizedLauncher()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        launcher.shutdown()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 运行启动器
    success = launcher.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
