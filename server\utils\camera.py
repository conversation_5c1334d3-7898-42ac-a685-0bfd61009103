import cv2

class CameraManager:
    def __init__(self):
        self.cameras = {}
    
    def get_camera(self, index, resolution='640x640', fps=30):
        if index in self.cameras:
            return self.cameras[index]

        # 解析分辨率
        width, height = map(int, resolution.split('x'))

        # 打开摄像头 - 低延时优化
        cap = cv2.VideoCapture(index, cv2.CAP_DSHOW)

        # 基本参数设置
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
        cap.set(cv2.CAP_PROP_FPS, fps)

        # 低延时优化设置
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 设置缓冲区大小为1，减少延时
        cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))  # 使用MJPEG格式

        # 自动曝光和白平衡优化
        cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # 手动曝光模式
        cap.set(cv2.CAP_PROP_EXPOSURE, -6)  # 减少曝光时间

        # 其他性能优化
        cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)  # 禁用自动对焦
        cap.set(cv2.CAP_PROP_AUTO_WB, 0)  # 禁用自动白平衡

        if not cap.isOpened():
            raise ValueError(f"无法打开摄像头索引 {index}")

        self.cameras[index] = cap
        return cap
    
    def release_all(self):
        for cap in self.cameras.values():
            cap.release()
        self.cameras.clear()
