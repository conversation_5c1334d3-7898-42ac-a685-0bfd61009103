worker_processes  auto;  # 自动检测CPU核心数

error_log  logs/error.log warn;  # 减少日志级别

events {
    worker_connections  1024;
    use epoll;  # Linux下使用epoll提高性能
    multi_accept on;  # 一次接受多个连接
}

rtmp {
    server {
        listen 1935;

        # 优化延时的核心配置
        chunk_size 128;          # 减小chunk大小降低延时 (默认4096)
        ping 3s;                 # 减少ping间隔
        ping_timeout 1s;         # 减少ping超时

        # 缓冲区优化
        buflen 100ms;            # 减少缓冲时间 (默认1000ms)

        # 连接优化
        max_streams 32;
        ack_window 5000000;
        max_message 1M;

        # 超时优化
        timeout 5s;              # 减少超时时间

        notify_method get;

        application live {
            live on;
            meta copy;

            # 低延时直播优化
            interleave on;           # 启用交错模式
            wait_key on;             # 等待关键帧
            wait_video on;           # 等待视频帧
            sync 10ms;               # 减少同步阈值 (默认300ms)
            drop_idle_publisher 5s;  # 快速断开空闲发布者

            # 访问控制
            allow publish 127.0.0.1;  # 只允许本机推流
            allow play all;          # 允许所有客户端播放
        }
    }
}

http {
    # HTTP性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # 压缩优化
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    server {
        listen      8080;

        # 性能优化
        access_log off;  # 关闭访问日志提高性能

        location / {
            root html;
        }

        location /stat {
            rtmp_stat all;
            rtmp_stat_stylesheet stat.xsl;

            # 禁用缓存确保实时数据
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        location /stat.xsl {
            root html;
        }

        # HLS低延时配置
        location /hls {
            types{
                application/vnd.apple.mpegurl m3u8;
                video/mp2t ts;
            }
            alias temp/hls;

            # 低延时HLS配置
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range";
        }
    }
}
