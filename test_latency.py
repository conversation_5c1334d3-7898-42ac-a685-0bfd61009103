#!/usr/bin/env python3
"""
延时测试脚本
用于测试和验证系统的延时性能
"""

import cv2
import time
import requests
import threading
import numpy as np
from collections import deque
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LatencyTester:
    """延时测试器"""
    
    def __init__(self, api_url="http://localhost:5000/api"):
        self.api_url = api_url
        self.latency_measurements = deque(maxlen=100)
        self.running = False
        
    def test_api_response(self):
        """测试API响应时间"""
        logger.info("Testing API response time...")
        
        response_times = []
        for i in range(10):
            start_time = time.time()
            try:
                response = requests.get(f"{self.api_url}/status", timeout=5)
                end_time = time.time()
                
                if response.status_code == 200:
                    response_time = (end_time - start_time) * 1000
                    response_times.append(response_time)
                    logger.info(f"API response {i+1}: {response_time:.1f}ms")
                else:
                    logger.error(f"API error: {response.status_code}")
            except Exception as e:
                logger.error(f"API request failed: {e}")
            
            time.sleep(0.5)
        
        if response_times:
            avg_response = sum(response_times) / len(response_times)
            max_response = max(response_times)
            min_response = min(response_times)
            
            logger.info(f"API Response Time - Avg: {avg_response:.1f}ms, "
                       f"Min: {min_response:.1f}ms, Max: {max_response:.1f}ms")
            return avg_response
        
        return None
    
    def test_camera_latency(self, camera_index=0):
        """测试摄像头延时"""
        logger.info("Testing camera latency...")
        
        cap = cv2.VideoCapture(camera_index, cv2.CAP_DSHOW)
        if not cap.isOpened():
            logger.error("Cannot open camera")
            return None
        
        # 应用优化设置
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))
        
        frame_times = []
        for i in range(50):
            start_time = time.time()
            ret, frame = cap.read()
            end_time = time.time()
            
            if ret:
                frame_time = (end_time - start_time) * 1000
                frame_times.append(frame_time)
                
                if i % 10 == 0:
                    logger.info(f"Camera frame {i+1}: {frame_time:.1f}ms")
            else:
                logger.error("Failed to read frame")
        
        cap.release()
        
        if frame_times:
            avg_frame_time = sum(frame_times) / len(frame_times)
            max_frame_time = max(frame_times)
            min_frame_time = min(frame_times)
            
            logger.info(f"Camera Latency - Avg: {avg_frame_time:.1f}ms, "
                       f"Min: {min_frame_time:.1f}ms, Max: {max_frame_time:.1f}ms")
            return avg_frame_time
        
        return None
    
    def test_detection_latency(self):
        """测试检测延时"""
        logger.info("Testing detection latency...")
        
        try:
            from server.utils.detection import Detector
            from server.config import MODEL_PATH
            
            # 初始化检测器
            detector = Detector(MODEL_PATH)
            
            # 创建测试图像
            test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            
            detection_times = []
            for i in range(20):
                start_time = time.time()
                detected_frame, counts = detector.detect(test_image)
                end_time = time.time()
                
                detection_time = (end_time - start_time) * 1000
                detection_times.append(detection_time)
                
                if i % 5 == 0:
                    logger.info(f"Detection {i+1}: {detection_time:.1f}ms")
            
            if detection_times:
                # 排除第一次（预热）
                detection_times = detection_times[1:]
                avg_detection = sum(detection_times) / len(detection_times)
                max_detection = max(detection_times)
                min_detection = min(detection_times)
                
                logger.info(f"Detection Latency - Avg: {avg_detection:.1f}ms, "
                           f"Min: {min_detection:.1f}ms, Max: {max_detection:.1f}ms")
                return avg_detection
                
        except Exception as e:
            logger.error(f"Detection test failed: {e}")
        
        return None
    
    def test_streaming_setup(self):
        """测试流媒体设置"""
        logger.info("Testing streaming setup...")
        
        # 测试RTMP连接
        rtmp_url = "rtmp://localhost/live/stream"
        
        try:
            # 使用FFmpeg测试RTMP连接
            import subprocess
            
            # 创建测试视频流
            cmd = [
                'ffmpeg', '-f', 'lavfi', '-i', 'testsrc=duration=5:size=640x640:rate=30',
                '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'zerolatency',
                '-f', 'flv', rtmp_url, '-y'
            ]
            
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            end_time = time.time()
            
            if result.returncode == 0:
                setup_time = (end_time - start_time) * 1000
                logger.info(f"Streaming setup successful: {setup_time:.1f}ms")
                return setup_time
            else:
                logger.error(f"Streaming setup failed: {result.stderr}")
                
        except Exception as e:
            logger.error(f"Streaming test failed: {e}")
        
        return None
    
    def run_comprehensive_test(self):
        """运行综合延时测试"""
        logger.info("=== Comprehensive Latency Test ===")
        
        results = {}
        
        # 1. API响应测试
        api_latency = self.test_api_response()
        if api_latency:
            results['api_response'] = api_latency
        
        # 2. 摄像头延时测试
        camera_latency = self.test_camera_latency()
        if camera_latency:
            results['camera_capture'] = camera_latency
        
        # 3. 检测延时测试
        detection_latency = self.test_detection_latency()
        if detection_latency:
            results['detection'] = detection_latency
        
        # 4. 流媒体设置测试
        streaming_latency = self.test_streaming_setup()
        if streaming_latency:
            results['streaming_setup'] = streaming_latency
        
        # 计算总延时估算
        if results:
            logger.info("\n=== Test Results Summary ===")
            total_latency = 0
            
            for component, latency in results.items():
                logger.info(f"{component.replace('_', ' ').title()}: {latency:.1f}ms")
                if component != 'streaming_setup':  # 设置时间不计入总延时
                    total_latency += latency
            
            logger.info(f"\nEstimated Total Latency: {total_latency:.1f}ms")
            
            # 性能评估
            if total_latency < 100:
                logger.info("🟢 Excellent performance - Very low latency")
            elif total_latency < 200:
                logger.info("🟡 Good performance - Low latency")
            elif total_latency < 500:
                logger.info("🟠 Moderate performance - Medium latency")
            else:
                logger.info("🔴 Poor performance - High latency")
            
            # 优化建议
            self.provide_optimization_suggestions(results)
        
        return results
    
    def provide_optimization_suggestions(self, results):
        """提供优化建议"""
        logger.info("\n=== Optimization Suggestions ===")
        
        suggestions = []
        
        if 'camera_capture' in results and results['camera_capture'] > 20:
            suggestions.append("Camera capture is slow - check camera settings and USB connection")
        
        if 'detection' in results and results['detection'] > 50:
            suggestions.append("Detection is slow - consider using GPU acceleration or reducing model complexity")
        
        if 'api_response' in results and results['api_response'] > 10:
            suggestions.append("API response is slow - check server performance and network")
        
        if not suggestions:
            suggestions.append("System performance is optimal!")
        
        for i, suggestion in enumerate(suggestions, 1):
            logger.info(f"{i}. {suggestion}")

def main():
    """主函数"""
    tester = LatencyTester()
    
    # 运行综合测试
    results = tester.run_comprehensive_test()
    
    if not results:
        logger.error("All tests failed - please check system configuration")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
